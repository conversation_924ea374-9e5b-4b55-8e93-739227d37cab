// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in aquapartner/test/presentation/widgets/streamlined_payment_flow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:aquapartner/core/services/analytics_service.dart' as _i4;
import 'package:aquapartner/core/services/payment_service.dart' as _i2;
import 'package:aquapartner/core/utils/logger.dart' as _i3;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i8;
import 'package:aquapartner/presentation/cubit/payment/payment_cubit.dart'
    as _i6;
import 'package:aquapartner/presentation/cubit/payment/payment_state.dart'
    as _i5;
import 'package:flutter_bloc/flutter_bloc.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePaymentService_0 extends _i1.SmartFake
    implements _i2.PaymentService {
  _FakePaymentService_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAppLogger_1 extends _i1.SmartFake implements _i3.AppLogger {
  _FakeAppLogger_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAnalyticsService_2 extends _i1.SmartFake
    implements _i4.AnalyticsService {
  _FakeAnalyticsService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentState_3 extends _i1.SmartFake implements _i5.PaymentState {
  _FakePaymentState_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PaymentCubit].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentCubit extends _i1.Mock implements _i6.PaymentCubit {
  MockPaymentCubit() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PaymentService get paymentService =>
      (super.noSuchMethod(
            Invocation.getter(#paymentService),
            returnValue: _FakePaymentService_0(
              this,
              Invocation.getter(#paymentService),
            ),
          )
          as _i2.PaymentService);

  @override
  _i3.AppLogger get logger =>
      (super.noSuchMethod(
            Invocation.getter(#logger),
            returnValue: _FakeAppLogger_1(this, Invocation.getter(#logger)),
          )
          as _i3.AppLogger);

  @override
  _i4.AnalyticsService get analyticsService =>
      (super.noSuchMethod(
            Invocation.getter(#analyticsService),
            returnValue: _FakeAnalyticsService_2(
              this,
              Invocation.getter(#analyticsService),
            ),
          )
          as _i4.AnalyticsService);

  @override
  _i5.PaymentState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakePaymentState_3(this, Invocation.getter(#state)),
          )
          as _i5.PaymentState);

  @override
  _i7.Stream<_i5.PaymentState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i7.Stream<_i5.PaymentState>.empty(),
          )
          as _i7.Stream<_i5.PaymentState>);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i7.Future<void> createPaymentLink(_i8.PaymentRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentLink, [request]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void startPaymentProcessing(String? paymentUrl) => super.noSuchMethod(
    Invocation.method(#startPaymentProcessing, [paymentUrl]),
    returnValueForMissingStub: null,
  );

  @override
  void updateWebViewLoading(bool? isLoading) => super.noSuchMethod(
    Invocation.method(#updateWebViewLoading, [isLoading]),
    returnValueForMissingStub: null,
  );

  @override
  void updateCurrentUrl(String? url) => super.noSuchMethod(
    Invocation.method(#updateCurrentUrl, [url]),
    returnValueForMissingStub: null,
  );

  @override
  void cancelPayment({String? reason = 'User cancelled'}) => super.noSuchMethod(
    Invocation.method(#cancelPayment, [], {#reason: reason}),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> validatePaymentUrl(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#validatePaymentUrl, [url]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void resetPayment() => super.noSuchMethod(
    Invocation.method(#resetPayment, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void emit(_i5.PaymentState? state) => super.noSuchMethod(
    Invocation.method(#emit, [state]),
    returnValueForMissingStub: null,
  );

  @override
  void onChange(_i9.Change<_i5.PaymentState>? change) => super.noSuchMethod(
    Invocation.method(#onChange, [change]),
    returnValueForMissingStub: null,
  );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
    Invocation.method(#addError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
    Invocation.method(#onError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i3.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}
