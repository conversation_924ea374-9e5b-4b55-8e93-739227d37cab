import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../core/config/payment_environment_config.dart';
import '../../core/services/payment_monitoring_service.dart';
import '../../core/services/payment_completion_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/invoices/invoice.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../../domain/entities/payments/payment_result.dart';
import '../../domain/services/auth_service.dart';
import '../cubit/invoices/invoices_cubit.dart';
import '../cubit/invoices/invoices_state.dart';
import '../cubit/payment/payment_cubit.dart';
import '../cubit/payment/payment_state.dart';
import 'payment/payment_webview.dart';
import '../../domain/entities/payments/payment_link.dart';
import '../../domain/entities/payments/payment_result.dart';
import '../../injection_container.dart' as di;

class InvoicesPage extends StatefulWidget {
  const InvoicesPage({super.key});

  @override
  State<InvoicesPage> createState() => _InvoicesPageState();
}

class _InvoicesPageState extends State<InvoicesPage> with AnalyticsMixin {
  @override
  String get screenName => 'InvoicesPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _invoiceTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<InvoicesCubit>().loadInvoices();

    // Track page initialization
    trackEvent('invoices_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'invoices_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'invoices_engagement',
      params: {
        'invoice_tap_count': _invoiceTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'invoices_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _trackInvoiceTap(Invoice invoice) {
    _invoiceTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'view_invoice_details',
      'table_row',
      elementId: invoice.invoiceNumber,
      additionalParams: {
        'invoice_number': invoice.invoiceNumber,
        'invoice_date': invoice.invoiceDate.toIso8601String(),
        'invoice_amount': invoice.total.toString(),
        'invoice_status': invoice.invoiceStatus,
        'invoice_tap_count': _invoiceTapCount.toString(),
        'time_on_screen_before_tap':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<InvoicesCubit, InvoicesState>(
      builder: (context, state) {
        if (state is InvoicesLoaded) {
          return state.invoices.isNotEmpty
              ? SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      StyledGenericTable<Invoice>(
                        items: state.invoices,
                        showDividers: true,
                        onRowTap: (invoice) {
                          _trackInvoiceTap(invoice);
                          // Add navigation logic here if needed
                        },
                        columns: [
                          ColumnConfig<Invoice>(
                            title: 'Date',
                            width: 100,
                            cellBuilder:
                                (invoice) => Align(
                                  alignment: Alignment.center,
                                  child: AquaText.body(
                                    DateFormat(
                                      'dd-MM-yyyy',
                                    ).format(invoice.invoiceDate),
                                  ),
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Total',
                            width: 100,
                            titleAlignment: Alignment.center,
                            bodyAlignment: Alignment.centerRight,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  CurrencyFormatter.formatAsINR(
                                    invoice.total,
                                    decimalPlaces: 0,
                                  ),
                                  weight: AquaFontWeight.bold,
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Invoice Number',
                            width: 150,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  invoice.invoiceNumber,
                                  weight: AquaFontWeight.semibold,
                                  color: acPrimaryBlue,
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Status',
                            width: 100,
                            cellBuilder:
                                (invoice) =>
                                    AquaText.body(invoice.invoiceStatus),
                          ),
                          ColumnConfig<Invoice>(
                            title: '',
                            width: 100,
                            cellBuilder:
                                (invoice) =>
                                    invoice.invoiceStatus == "Overdue"
                                        ? TextButton(
                                          child: AquaText.body(
                                            "Pay Now",
                                            color: acPrimaryBlue,
                                            weight: AquaFontWeight.bold,
                                          ),
                                          onPressed:
                                              () => _handlePayNowPressed(
                                                context,
                                                invoice,
                                              ),
                                        )
                                        : Container(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
              : Center(
                child: AquaText.subheadline("You dont have any invoices"),
              );
        }
        return LoadingWidget(message: "Please wait, your invoices loading.");
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is InvoicesLoading) {
          trackEvent('invoices_loading');
        } else if (state is InvoicesLoaded) {
          _lastInteractionTime =
              DateTime.now(); // Update interaction time when data loads
          trackEvent(
            'invoices_loaded',
            params: {
              'invoice_count': state.invoices.length.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
              'time_to_load':
                  _screenViewStartTime != null
                      ? DateTime.now()
                          .difference(_screenViewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state is InvoiceError) {
          trackEvent(
            'invoices_error',
            params: {'error_message': state.message},
          );
        }
      },
    );
  }

  /// Handle secure payment initiation for overdue invoices - STREAMLINED FLOW
  void _handlePayNowPressed(BuildContext context, Invoice invoice) async {
    // Store context reference before async operations
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final logger = di.sl<AppLogger>();

    try {
      // Track payment initiation attempt
      PaymentMonitoringService.trackSecurityEvent(
        eventType: 'payment_initiation_attempt_direct',
        description:
            'User attempted direct payment for invoice: ${invoice.invoiceNumber}',
        identifier: invoice.customerId,
      );

      // Debug: Log payment initiation start
      logger.d(
        'Starting DIRECT payment initiation for invoice: ${invoice.invoiceNumber}',
      );

      // Validate invoice is eligible for payment
      if (invoice.invoiceStatus != "Overdue") {
        logger.w('Invoice not eligible - status: ${invoice.invoiceStatus}');
        _showErrorDialog(context, 'This invoice is not eligible for payment.');
        return;
      }

      logger.d('Invoice validation passed');

      // Validate environment configuration
      try {
        PaymentEnvironmentConfig.validateConfiguration();
        logger.d('Environment configuration validation passed');
      } catch (e) {
        logger.e('Environment configuration validation failed: $e');
        _showErrorDialog(
          context,
          'Payment system is currently unavailable. Please try again later.',
        );
        PaymentMonitoringService.trackPaymentError(
          errorType: 'ConfigurationError',
          errorMessage: 'Payment environment configuration invalid: $e',
        );
        return;
      }

      // Show loading indicator
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Creating payment link...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Create payment request
      logger.d('Creating payment request for amount: ${invoice.total}');
      final paymentRequest = PaymentRequest(
        amount: invoice.total,
        description: 'Payment for Invoice ${invoice.invoiceNumber}',
        customerEmail:
            '<EMAIL>', // Would get from customer data in production
        currency: 'INR',
        invoiceNumber: invoice.invoiceNumber,
        customerId: invoice.customerId,
        customerName:
            'Customer Name', // Would get from customer data in production
        metadata: {
          'invoice_id': invoice.invoiceId.toString(),
          'invoice_number': invoice.invoiceNumber,
          'source': 'invoices_page_direct',
          'environment': PaymentEnvironmentConfig.environment,
          'flow_type': 'streamlined',
        },
      );

      logger.d('Payment request created successfully');

      // Validate payment request
      final validationErrors = paymentRequest.validate();
      if (validationErrors.isNotEmpty) {
        logger.e(
          'Payment request validation failed: ${validationErrors.join(', ')}',
        );
        _showErrorDialog(
          context,
          'Invalid payment data: ${validationErrors.join(', ')}',
        );
        PaymentMonitoringService.trackPaymentError(
          errorType: 'ValidationError',
          errorMessage:
              'Payment request validation failed: ${validationErrors.join(', ')}',
        );
        return;
      }

      logger.d('Payment request validation passed');

      // STREAMLINED FLOW: Create payment link and navigate directly to WebView
      logger.d('Creating payment link and navigating directly to WebView');

      try {
        // Create PaymentCubit with detailed error handling
        PaymentCubit? paymentCubit;
        try {
          paymentCubit = di.sl<PaymentCubit>();
          logger.d('PaymentCubit created successfully');
        } catch (dependencyError) {
          logger.e('Failed to create PaymentCubit: $dependencyError');
          logger.e('Dependency error type: ${dependencyError.runtimeType}');

          // Show user-friendly error message
          if (mounted) {
            _showErrorDialog(
              context,
              'Payment system is currently unavailable. Please try again later or contact support.',
            );
          }

          PaymentMonitoringService.trackPaymentError(
            errorType: 'DependencyInjectionError',
            errorMessage: 'Failed to create PaymentCubit: $dependencyError',
          );
          return;
        }

        // Create payment link directly
        logger.d('Creating payment link via PaymentCubit');

        // Track direct payment flow initiation
        PaymentMonitoringService.trackPaymentInitiated(
          paymentId: 'invoice_${invoice.invoiceNumber}',
          amount: invoice.total,
          currency: 'INR',
          customerEmail: paymentRequest.customerEmail,
          invoiceNumber: invoice.invoiceNumber,
          customerId: invoice.customerId,
          metadata: paymentRequest.metadata,
        );

        final result = await _createPaymentLinkAndNavigateToWebView(
          navigator,
          paymentCubit,
          paymentRequest,
          invoice,
        );

        logger.d('Navigation completed, result: $result');

        // Handle payment result
        if (result != null && result.isSuccess) {
          logger.i('Payment was successful, handling success');
          // Payment was successful, now verify transaction and refresh invoices
          if (mounted) {
            await _handlePaymentSuccess(context, invoice, result);
          }
        } else {
          logger.w('Payment was not successful or result is null');
        }
      } catch (navigationError) {
        logger.e('Navigation error: $navigationError');
        logger.e('Navigation error type: ${navigationError.runtimeType}');
        logger.e('Navigation error stack trace: ${StackTrace.current}');
        rethrow;
      }
    } catch (e, stackTrace) {
      logger.e('Caught exception in _handlePayNowPressed: $e');
      logger.e('Exception type: ${e.runtimeType}');
      logger.e('Stack trace: $stackTrace');

      if (mounted) {
        _showErrorDialog(
          context,
          'Failed to initiate payment. Please try again. Error: $e',
        );
      }

      PaymentMonitoringService.trackPaymentError(
        errorType: 'UnexpectedError',
        errorMessage: 'Failed to handle pay now: $e',
      );
    }
  }

  /// Show error dialog with secure logging
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Payment Error'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  /// Handle successful payment completion with transaction verification
  Future<void> _handlePaymentSuccess(
    BuildContext context,
    Invoice invoice,
    PaymentResult paymentResult,
  ) async {
    // Store context references before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final invoicesCubit = context.read<InvoicesCubit>();
    final authService = context.read<AuthService>();
    final paymentCompletionService = context.read<PaymentCompletionService>();

    try {
      // Show initial success message
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text(
            'Payment completed successfully! Verifying transaction and updating invoice status...',
          ),
          duration: Duration(seconds: 3),
        ),
      );

      // Check if we have a transaction ID for verification
      if (paymentResult.transactionId != null &&
          paymentResult.transactionId!.isNotEmpty) {
        // Use PaymentCompletionService for comprehensive verification
        final completionResult = await paymentCompletionService
            .completePaymentFlow(
              transactionId: paymentResult.transactionId!,
              invoiceNumber: invoice.invoiceNumber,
              forceRefreshIfNeeded: true,
              retryDelay: const Duration(seconds: 2),
            );

        if (completionResult.isSuccess) {
          // Transaction verified and invoice updated successfully
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(completionResult.statusMessage),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );

          // Refresh invoices to show updated status
          final customerResult = await authService.getCurrentCustomer();
          customerResult.fold((failure) => null, (customer) {
            if (customer != null) {
              invoicesCubit.invalidateAndSync(customer.customerId);
            }
          });
        } else if (completionResult.isPartialSuccess) {
          // Payment successful but invoice not auto-updated
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(completionResult.statusMessage),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
            ),
          );

          // Still refresh invoices in case status was updated
          final customerResult = await authService.getCurrentCustomer();
          customerResult.fold((failure) => null, (customer) {
            if (customer != null) {
              invoicesCubit.invalidateAndSync(customer.customerId);
            }
          });
        } else {
          // Verification failed, fallback to manual refresh
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(completionResult.statusMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );

          // Still try to refresh invoices
          await _fallbackInvoiceRefresh(authService, invoicesCubit, invoice);
        }
      } else {
        // No transaction ID available, fallback to simple refresh
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Payment completed! Refreshing invoice list...'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );

        await _fallbackInvoiceRefresh(authService, invoicesCubit, invoice);
      }

      // Track payment completion
      PaymentMonitoringService.trackPaymentCompleted(
        paymentId:
            paymentResult.paymentId ?? 'invoice_${invoice.invoiceNumber}',
        status: 'completed',
        amount: invoice.total,
        currency: 'INR',
        totalTime: const Duration(seconds: 5),
      );
    } catch (e) {
      // Handle any errors in payment success processing
      PaymentMonitoringService.trackPaymentError(
        errorType: 'PaymentSuccessHandlingError',
        errorMessage: 'Error handling payment success: $e',
      );

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text(
              'Payment was successful, but there was an issue verifying the transaction. Please refresh manually.',
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// Fallback method to refresh invoices when transaction verification is not available
  Future<void> _fallbackInvoiceRefresh(
    AuthService authService,
    InvoicesCubit invoicesCubit,
    Invoice invoice,
  ) async {
    final customerResult = await authService.getCurrentCustomer();

    customerResult.fold(
      (failure) {
        PaymentMonitoringService.trackPaymentError(
          errorType: 'CustomerRetrievalError',
          errorMessage:
              'Failed to get customer for invoice refresh: ${failure.toString()}',
        );
      },
      (customer) {
        if (customer != null) {
          invoicesCubit.invalidateAndSync(customer.customerId);

          PaymentMonitoringService.trackSecurityEvent(
            eventType: 'invoice_refresh_after_payment_fallback',
            description: 'Fallback invoice refresh after payment completion',
            identifier: customer.customerId,
            securityData: {
              'invoice_number': invoice.invoiceNumber,
              'payment_amount': invoice.total.toString(),
              'reason': 'no_transaction_id',
            },
          );
        }
      },
    );
  }

  /// STREAMLINED FLOW: Create payment link and navigate directly to WebView
  Future<PaymentResult?> _createPaymentLinkAndNavigateToWebView(
    NavigatorState navigator,
    PaymentCubit paymentCubit,
    PaymentRequest paymentRequest,
    Invoice invoice,
  ) async {
    final logger = di.sl<AppLogger>();

    try {
      logger.d('Creating payment link for direct WebView navigation');

      // Create payment link
      paymentCubit.createPaymentLink(paymentRequest);

      // Wait for payment link creation with timeout
      PaymentLink? paymentLink;
      String? errorMessage;

      await for (final state in paymentCubit.stream) {
        if (state is PaymentLinkCreated) {
          logger.i('Payment link created successfully: ${state}');
          paymentLink = state.paymentLink;
          logger.i(
            'Payment link created successfully: ${paymentLink.paymentLinkId}',
          );
          break;
        } else if (state is PaymentError) {
          errorMessage = state.message;
          logger.e('Payment link creation failed: $errorMessage');
          break;
        }

        // Add timeout protection
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (paymentLink == null) {
        throw Exception(errorMessage ?? 'Failed to create payment link');
      }

      // Track successful payment link creation
      PaymentMonitoringService.trackPaymentLinkCreated(
        paymentLinkId: paymentLink.paymentLinkId,
        paymentLinkUrl: paymentLink.paymentLinkUrl,
        amount: paymentLink.amount,
        currency: paymentLink.currency,
        customerEmail: paymentRequest.customerEmail,
        responseTime: const Duration(seconds: 1), // Approximate
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      // Navigate directly to WebView
      logger.d('Navigating directly to PaymentWebView');

      // Listen to payment completion through PaymentCubit states
      PaymentResult? paymentResult;

      final result = await navigator.push<PaymentResult>(
        MaterialPageRoute(
          builder:
              (context) => BlocProvider.value(
                value: paymentCubit,
                child: BlocListener<PaymentCubit, PaymentState>(
                  listener: (context, state) {
                    if (state is PaymentCompleted) {
                      logger.d(
                        'Payment completed successfully: ${state.result}',
                      );
                      paymentResult = state.result;
                      Navigator.of(context).pop(state.result);
                    } else if (state is PaymentCancelled) {
                      logger.d('Payment cancelled by user');
                      paymentResult = PaymentResult.cancelled(
                        invoiceNumber: invoice.invoiceNumber,
                        paymentLinkId: paymentLink?.paymentLinkId,
                      );
                      Navigator.of(context).pop(paymentResult);
                    } else if (state is PaymentError) {
                      logger.e('Payment error: ${state.message}');
                      paymentResult = PaymentResult.failed(
                        errorMessage: state.message,
                        invoiceNumber: invoice.invoiceNumber,
                        paymentLinkId: paymentLink?.paymentLinkId,
                      );
                      Navigator.of(context).pop(paymentResult);
                    }
                  },
                  child: PaymentWebView(
                    paymentLink: paymentLink!,
                    onCancel: () {
                      logger.d('Payment cancelled via WebView onCancel');
                      paymentCubit.cancelPayment();
                    },
                  ),
                ),
              ),
        ),
      );

      logger.d('WebView navigation completed with result: $result');
      return result ?? paymentResult;
    } catch (e, stackTrace) {
      logger.e('Error in direct payment flow: $e');
      logger.e('Stack trace: $stackTrace');

      PaymentMonitoringService.trackPaymentError(
        errorType: 'DirectPaymentFlowError',
        errorMessage:
            'Failed to create payment link and navigate to WebView: $e',
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      rethrow;
    }
  }
}
