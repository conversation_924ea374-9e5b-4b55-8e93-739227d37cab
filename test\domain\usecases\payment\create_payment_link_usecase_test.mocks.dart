// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in aquapartner/test/domain/usecases/payment/create_payment_link_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:aquapartner/core/error/failures.dart' as _i6;
import 'package:aquapartner/domain/entities/payments/payment_link.dart' as _i7;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i8;
import 'package:aquapartner/domain/entities/payments/payment_result.dart'
    as _i3;
import 'package:aquapartner/domain/entities/payments/payment_verification_response.dart'
    as _i9;
import 'package:aquapartner/domain/repositories/payment_repository.dart' as _i4;
import 'package:dartz/dartz.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentResult_1 extends _i1.SmartFake implements _i3.PaymentResult {
  _FakePaymentResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PaymentRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentRepository extends _i1.Mock implements _i4.PaymentRepository {
  MockPaymentRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>> createPaymentLink(
    _i8.PaymentRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentLink, [request]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>>.value(
                  _FakeEither_0<_i6.Failure, _i7.PaymentLink>(
                    this,
                    Invocation.method(#createPaymentLink, [request]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>> createPaymentSession(
    _i8.PaymentRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentSession, [request]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>>.value(
                  _FakeEither_0<_i6.Failure, _i7.PaymentLink>(
                    this,
                    Invocation.method(#createPaymentSession, [request]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.PaymentLink>>);

  @override
  bool validatePaymentUrl(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#validatePaymentUrl, [url]),
            returnValue: false,
          )
          as bool);

  @override
  _i3.PaymentResult parsePaymentResult(String? callbackUrl) =>
      (super.noSuchMethod(
            Invocation.method(#parsePaymentResult, [callbackUrl]),
            returnValue: _FakePaymentResult_1(
              this,
              Invocation.method(#parsePaymentResult, [callbackUrl]),
            ),
          )
          as _i3.PaymentResult);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i3.PaymentResult>> getPaymentStatus(
    String? paymentLinkId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentStatus, [paymentLinkId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i3.PaymentResult>>.value(
                  _FakeEither_0<_i6.Failure, _i3.PaymentResult>(
                    this,
                    Invocation.method(#getPaymentStatus, [paymentLinkId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i3.PaymentResult>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, bool>> cancelPaymentLink(
    String? paymentLinkId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelPaymentLink, [paymentLinkId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, bool>>.value(
              _FakeEither_0<_i6.Failure, bool>(
                this,
                Invocation.method(#cancelPaymentLink, [paymentLinkId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, bool>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>>
  verifyTransaction(String? transactionId) =>
      (super.noSuchMethod(
            Invocation.method(#verifyTransaction, [transactionId]),
            returnValue: _i5.Future<
              _i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>
            >.value(
              _FakeEither_0<_i6.Failure, _i9.PaymentVerificationResponse>(
                this,
                Invocation.method(#verifyTransaction, [transactionId]),
              ),
            ),
          )
          as _i5.Future<
            _i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>
          >);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>>
  forceRefreshTransaction(
    String? transactionId, {
    bool? updateInvoice = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #forceRefreshTransaction,
              [transactionId],
              {#updateInvoice: updateInvoice},
            ),
            returnValue: _i5.Future<
              _i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>
            >.value(
              _FakeEither_0<_i6.Failure, _i9.PaymentVerificationResponse>(
                this,
                Invocation.method(
                  #forceRefreshTransaction,
                  [transactionId],
                  {#updateInvoice: updateInvoice},
                ),
              ),
            ),
          )
          as _i5.Future<
            _i2.Either<_i6.Failure, _i9.PaymentVerificationResponse>
          >);
}
