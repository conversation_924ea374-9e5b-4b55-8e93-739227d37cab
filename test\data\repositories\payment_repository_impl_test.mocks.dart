// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in aquapartner/test/data/repositories/payment_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:aquapartner/core/utils/logger.dart' as _i7;
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart'
    as _i4;
import 'package:aquapartner/data/models/payment/payment_link_model.dart' as _i2;
import 'package:aquapartner/data/models/payment/payment_request_model.dart'
    as _i6;
import 'package:aquapartner/data/models/payment/payment_verification_response_model.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePaymentLinkModel_0 extends _i1.SmartFake
    implements _i2.PaymentLinkModel {
  _FakePaymentLinkModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentVerificationResponseModel_1 extends _i1.SmartFake
    implements _i3.PaymentVerificationResponseModel {
  _FakePaymentVerificationResponseModel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [PaymentRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentRemoteDataSource extends _i1.Mock
    implements _i4.PaymentRemoteDataSource {
  MockPaymentRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.PaymentLinkModel> createPaymentLink(
    _i6.PaymentRequestModel? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentLink, [request]),
            returnValue: _i5.Future<_i2.PaymentLinkModel>.value(
              _FakePaymentLinkModel_0(
                this,
                Invocation.method(#createPaymentLink, [request]),
              ),
            ),
          )
          as _i5.Future<_i2.PaymentLinkModel>);

  @override
  _i5.Future<_i2.PaymentLinkModel> createPaymentSession(
    _i6.PaymentRequestModel? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentSession, [request]),
            returnValue: _i5.Future<_i2.PaymentLinkModel>.value(
              _FakePaymentLinkModel_0(
                this,
                Invocation.method(#createPaymentSession, [request]),
              ),
            ),
          )
          as _i5.Future<_i2.PaymentLinkModel>);

  @override
  _i5.Future<_i2.PaymentLinkModel> createFlutterPayment(
    _i6.PaymentRequestModel? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createFlutterPayment, [request]),
            returnValue: _i5.Future<_i2.PaymentLinkModel>.value(
              _FakePaymentLinkModel_0(
                this,
                Invocation.method(#createFlutterPayment, [request]),
              ),
            ),
          )
          as _i5.Future<_i2.PaymentLinkModel>);

  @override
  _i5.Future<_i2.PaymentLinkModel> getPaymentStatus(String? paymentLinkId) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentStatus, [paymentLinkId]),
            returnValue: _i5.Future<_i2.PaymentLinkModel>.value(
              _FakePaymentLinkModel_0(
                this,
                Invocation.method(#getPaymentStatus, [paymentLinkId]),
              ),
            ),
          )
          as _i5.Future<_i2.PaymentLinkModel>);

  @override
  _i5.Future<_i3.PaymentVerificationResponseModel> verifyTransaction(
    String? transactionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyTransaction, [transactionId]),
            returnValue: _i5.Future<_i3.PaymentVerificationResponseModel>.value(
              _FakePaymentVerificationResponseModel_1(
                this,
                Invocation.method(#verifyTransaction, [transactionId]),
              ),
            ),
          )
          as _i5.Future<_i3.PaymentVerificationResponseModel>);

  @override
  _i5.Future<_i3.PaymentVerificationResponseModel> forceRefreshTransaction(
    String? transactionId, {
    bool? updateInvoice = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #forceRefreshTransaction,
              [transactionId],
              {#updateInvoice: updateInvoice},
            ),
            returnValue: _i5.Future<_i3.PaymentVerificationResponseModel>.value(
              _FakePaymentVerificationResponseModel_1(
                this,
                Invocation.method(
                  #forceRefreshTransaction,
                  [transactionId],
                  {#updateInvoice: updateInvoice},
                ),
              ),
            ),
          )
          as _i5.Future<_i3.PaymentVerificationResponseModel>);
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i7.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}
